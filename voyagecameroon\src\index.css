@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .glass-card {
    @apply bg-white/10 backdrop-blur-lg border border-white/20 shadow-xl;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-cameroon-green-400 via-cameroon-yellow-400 to-cameroon-red-400 bg-clip-text text-transparent;
  }

  .bg-cameroon-gradient {
    @apply bg-gradient-to-r from-cameroon-green-500 via-cameroon-yellow-500 to-cameroon-red-500;
  }

  .cameroon-gradient-bg {
    @apply bg-gradient-to-br from-cameroon-green-500 via-cameroon-blue-500 to-cameroon-red-500;
  }

  .futuristic-button {
    @apply px-8 py-4 bg-gradient-to-r from-cameroon-green-500 to-cameroon-blue-500
           rounded-full text-white font-semibold hover:from-cameroon-green-600
           hover:to-cameroon-blue-600 transform hover:scale-105 transition-all
           duration-300 shadow-xl hover:shadow-2xl;
  }

  .section-padding {
    @apply py-20 lg:py-32;
  }

  .container-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }
  
  /* Toggle Checkbox Styling */
  .toggle-checkbox:checked {
    @apply right-0 border-cameroon-green-500;
  }
  
  .toggle-checkbox:checked + .toggle-label {
    @apply bg-cameroon-green-500;
  }
  
  .toggle-checkbox {
    @apply right-4 border-gray-400;
  }
  
  .toggle-label {
    @apply block overflow-hidden h-6 rounded-full bg-gray-400 cursor-pointer;
  }
  
  /* Line Clamp */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
  }

  .animate-on-scroll.animate {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #22c55e, #3b82f6);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #16a34a, #2563eb);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}
